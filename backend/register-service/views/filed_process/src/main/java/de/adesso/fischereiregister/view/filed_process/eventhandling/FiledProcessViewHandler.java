package de.adesso.fischereiregister.view.filed_process.eventhandling;

import de.adesso.fischereiregister.core.events.BannedEvent;
import de.adesso.fischereiregister.core.events.FishingTaxPayedEvent;
import de.adesso.fischereiregister.core.events.JurisdictionMovedEvent;
import de.adesso.fischereiregister.core.events.LicenseExtendedEvent;
import de.adesso.fischereiregister.core.events.LimitedLicenseApplicationCreatedEvent;
import de.adesso.fischereiregister.core.events.LimitedLicenseApplicationRejectedEvent;
import de.adesso.fischereiregister.core.events.LimitedLicenseCreatedEvent;
import de.adesso.fischereiregister.core.events.PersonCreatedEvent;
import de.adesso.fischereiregister.core.events.QualificationsProofCreatedEvent;
import de.adesso.fischereiregister.core.events.RegularLicenseCreatedEvent;
import de.adesso.fischereiregister.core.events.RegularLicenseDigitizedEvent;
import de.adesso.fischereiregister.core.events.ReplacementCardOrderedEvent;
import de.adesso.fischereiregister.core.events.UnbannedEvent;
import de.adesso.fischereiregister.core.events.VacationLicenseCreatedEvent;
import de.adesso.fischereiregister.core.model.Address;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.registerservice.import_testdata.MockedTimestampService;
import de.adesso.fischereiregister.view.filed_process.FiledProcessViewService;
import de.adesso.fischereiregister.view.filed_process.ProcessHeaderData;
import org.axonframework.eventhandling.EventHandler;
import org.axonframework.eventhandling.ResetHandler;
import org.axonframework.eventhandling.Timestamp;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;

/**
 * All events besides the PersonalDataChangedEvent are listed in the process file. For each event the process with the
 * corresponding data is created.
 */
@Component
public class FiledProcessViewHandler {
    private final FiledProcessViewService filedProcessViewService;
    private final MockedTimestampService mockedTimestampService;

    public FiledProcessViewHandler(FiledProcessViewService filedProcessViewService, MockedTimestampService mockedTimestampService) {
        this.filedProcessViewService = filedProcessViewService;
        this.mockedTimestampService = mockedTimestampService;
    }

    @ResetHandler
    @Transactional
    public void onReset() {
        filedProcessViewService.truncateView();
    }


    @EventHandler
    @Transactional
    public void on(BannedEvent event, @Timestamp Instant timestamp) {
        Instant effectiveTimestamp = mockedTimestampService.getEffectiveTimestamp(timestamp);
        ProcessHeaderData processHeaderData = ProcessHeaderData.builder()
                .registerEntryId(event.registerId())
                .actingInstitution(null)
                .federalStateOfInstitution(FederalState.valueOf(event.jurisdiction().getFederalState()))
                .timestamp(effectiveTimestamp)
                .build();

        filedProcessViewService.createBannedProcess(processHeaderData, event.ban());
    }

    @EventHandler
    @Transactional
    public void on(FishingTaxPayedEvent event, @Timestamp Instant timestamp) {
        Instant effectiveTimestamp = mockedTimestampService.getEffectiveTimestamp(timestamp);
        ProcessHeaderData processHeaderData = ProcessHeaderData.builder()
                .registerEntryId(event.registerId())
                .actingInstitution(null)
                .federalStateOfInstitution(FederalState.valueOf(event.taxes().getFirst().getFederalState()))
                .timestamp(effectiveTimestamp)
                .build();

        filedProcessViewService.createFishingTaxPayedProcess(
                processHeaderData,
                event.person(),
                event.serviceAccountId(),
                event.taxes(),
                event.consentInfo(),
                event.identificationDocuments()
        );
    }

    @EventHandler
    @Transactional
    public void on(JurisdictionMovedEvent event, @Timestamp Instant timestamp) {
        Instant effectiveTimestamp = mockedTimestampService.getEffectiveTimestamp(timestamp);
        ProcessHeaderData processHeaderData = ProcessHeaderData.builder()
                .registerEntryId(event.registerId())
                .actingInstitution(null)
                .federalStateOfInstitution(FederalState.valueOf(event.newJurisdiction().getFederalState()))
                .timestamp(effectiveTimestamp)
                .build();

        filedProcessViewService.createJurisdictionMovedProcess(
                processHeaderData,
                event.taxes(),
                event.consentInfo(),
                event.identificationDocuments(),
                event.newJurisdiction()
        );
    }

    @EventHandler
    @Transactional
    public void on(LicenseExtendedEvent event, @Timestamp Instant timestamp) {
        Instant effectiveTimestamp = mockedTimestampService.getEffectiveTimestamp(timestamp);
        ProcessHeaderData processHeaderData = ProcessHeaderData.builder()
                .registerEntryId(event.registerEntryId())
                .actingInstitution(event.issuedByOffice())
                .federalStateOfInstitution(FederalState.valueOf(
                        event.fees().getFirst().getFederalState()))
                .timestamp(effectiveTimestamp)
                .federalState(null)
                .issuedBy(event.issuedByOffice())
                .build();

        filedProcessViewService.createFishingLicenseExtendedProcess(
                processHeaderData,
                event.person(),
                event.serviceAccountId(),
                event.taxes(),
                event.fees(),
                event.identificationDocuments(),
                event.licenseNumber(),
                event.person().getOfficeAddress()
        );
    }

    @EventHandler
    @Transactional
    public void on(LimitedLicenseCreatedEvent event, @Timestamp Instant timestamp) {
        Instant effectiveTimestamp = mockedTimestampService.getEffectiveTimestamp(timestamp);
        ProcessHeaderData processHeaderData = ProcessHeaderData.builder()
                .registerEntryId(event.registerId())
                .actingInstitution(event.issuedByOffice())
                .federalStateOfInstitution(event.fishingLicense().getIssuingFederalState())
                .timestamp(effectiveTimestamp)
                .federalState(FederalState.valueOf(event.jurisdiction().getFederalState()))
                .issuedBy(event.issuedByOffice())
                .build();

        filedProcessViewService.createFishingLicenseCreatedProcess(
                processHeaderData,
                event.person(),
                event.serviceAccountId(),
                event.taxes(),
                event.fees(),
                event.identificationDocuments(),
                event.fishingLicense(),
                event.person().getOfficeAddress()
        );
    }

    @EventHandler
    @Transactional
    public void on(LimitedLicenseApplicationCreatedEvent event, @Timestamp Instant timestamp) {
        ProcessHeaderData processHeaderData = ProcessHeaderData.builder()
                .registerEntryId(event.registerEntryId())
                .actingInstitution(null)
                .federalStateOfInstitution(event.limitedLicenseApplication().getFederalState())
                .timestamp(mockedTimestampService.getEffectiveTimestamp(timestamp))
                .build();

        filedProcessViewService.createLimitedLicenseApplicationProcess(
                processHeaderData,
                event.person(),
                event.serviceAccountId(),
                event.fees(),
                event.consentInfo()
        );
    }

    @EventHandler
    @Transactional
    public void on(LimitedLicenseApplicationRejectedEvent event, @Timestamp Instant timestamp) {
        ProcessHeaderData processHeaderData = ProcessHeaderData.builder()
                .registerEntryId(event.registerEntryId())
                .actingInstitution(null)
                .federalStateOfInstitution(event.limitedLicenseApplication().getFederalState())
                .timestamp(mockedTimestampService.getEffectiveTimestamp(timestamp))
                .build();

        filedProcessViewService.createLimitedLicenseRejectedProcess(processHeaderData);
    }

    @EventHandler
    @Transactional
    public void on(PersonCreatedEvent event, @Timestamp Instant timestamp) {
        ProcessHeaderData processHeaderData = ProcessHeaderData.builder()
                .registerEntryId(event.registerId())
                .actingInstitution(event.issuedByOffice())
                .federalStateOfInstitution(FederalState.valueOf(event.federalState()))
                .timestamp(mockedTimestampService.getEffectiveTimestamp(timestamp))
                .build();

        filedProcessViewService.createFishingTaxPayedProcess(
                processHeaderData,
                event.person(),
                event.serviceAccountId(),
                event.taxes(),
                event.consentInfo(),
                event.identificationDocuments()
        );
    }

    @EventHandler
    @Transactional
    public void on(QualificationsProofCreatedEvent event, @Timestamp Instant timestamp) {
        ProcessHeaderData processHeaderData = ProcessHeaderData.builder()
                .registerEntryId(event.registerEntryId())
                .actingInstitution(null)
                .federalStateOfInstitution(FederalState.valueOf(
                        event.qualificationsProof().getFederalState()))
                .timestamp(mockedTimestampService.getEffectiveTimestamp(timestamp))
                .build();

        filedProcessViewService.createQualificationsProofCreatedProcess(
                processHeaderData,
                event.person(),
                event.qualificationsProof()
        );
    }

    @EventHandler
    @Transactional
    public void on(RegularLicenseCreatedEvent event, @Timestamp Instant timestamp) {
        ProcessHeaderData processHeaderData = ProcessHeaderData.builder()
                .registerEntryId(event.registerId())
                .actingInstitution(event.issuedByOffice())
                .federalStateOfInstitution(event.fishingLicense().getIssuingFederalState())
                .timestamp(mockedTimestampService.getEffectiveTimestamp(timestamp))
                .federalState(FederalState.valueOf(event.jurisdiction().getFederalState()))
                .issuedBy(event.issuedByOffice())
                .build();

        filedProcessViewService.createFishingLicenseCreatedProcess(
                processHeaderData,
                event.person(),
                event.serviceAccountId(),
                event.taxes(),
                event.fees(),
                event.identificationDocuments(),
                event.fishingLicense(),
                event.person().getOfficeAddress()
        );
    }

    @EventHandler
    @Transactional
    public void on(RegularLicenseDigitizedEvent event, @Timestamp Instant timestamp) {
        ProcessHeaderData processHeaderData = ProcessHeaderData.builder()
                .registerEntryId(event.registerId())
                .actingInstitution(event.issuedByOffice())
                .federalStateOfInstitution(event.fishingLicense().getIssuingFederalState())
                .timestamp(mockedTimestampService.getEffectiveTimestamp(timestamp))
                .federalState(FederalState.valueOf(event.jurisdiction().getFederalState()))
                .issuedBy(event.issuedByOffice())
                .build();

        filedProcessViewService.createFishingLicenseCreatedProcess(
                processHeaderData,
                event.person(),
                null,
                event.taxes(),
                event.fees(),
                event.identificationDocuments(),
                event.fishingLicense(),
                event.person().getOfficeAddress()
        );
    }

    @EventHandler
    @Transactional
    public void on(ReplacementCardOrderedEvent event, @Timestamp Instant timestamp) {
        ProcessHeaderData processHeaderData = ProcessHeaderData.builder()
                .registerEntryId(event.registerId())
                .actingInstitution(event.issuedByOffice())
                .federalStateOfInstitution(event.federalState())
                .timestamp(mockedTimestampService.getEffectiveTimestamp(timestamp))
                .federalState(event.federalState())
                .issuedBy(event.issuedByOffice())
                .build();

        filedProcessViewService.createReplacementCardOrderedProcess(
                processHeaderData,
                event.person(),
                event.serviceAccountId(),
                event.taxes(),
                event.fees(),
                event.identificationDocuments(),
                event.fishingLicense(),
                getAddress(event.person())
        );
    }

    @EventHandler
    @Transactional
    public void on(UnbannedEvent event, @Timestamp Instant timestamp) {
        ProcessHeaderData processHeaderData = ProcessHeaderData.builder()
                .registerEntryId(event.registerEntryId())
                .federalStateOfInstitution(FederalState.valueOf(event.jurisdiction().getFederalState()))
                .timestamp(mockedTimestampService.getEffectiveTimestamp(timestamp))
                .build();

        filedProcessViewService.createUnbannedProcess(processHeaderData);
    }

    @EventHandler
    @Transactional
    public void on(VacationLicenseCreatedEvent event, @Timestamp Instant timestamp) {
        ProcessHeaderData processHeaderData = ProcessHeaderData.builder()
                .registerEntryId(event.registerEntryId())
                .actingInstitution(event.issuedByOffice())
                .federalStateOfInstitution(event.fishingLicense().getIssuingFederalState())
                .timestamp(mockedTimestampService.getEffectiveTimestamp(timestamp))
                .federalState(event.fishingLicense().getIssuingFederalState())
                .issuedBy(event.issuedByOffice())
                .build();

        filedProcessViewService.createFishingLicenseCreatedProcess(
                processHeaderData,
                event.person(),
                event.serviceAccountId(),
                event.taxes(),
                event.fees(),
                event.identificationDocuments(),
                event.fishingLicense(),
                event.person().getOfficeAddress()
        );
    }

    private Address getAddress(Person person) {
        if (person.getAddress() != null) {
            return person.getAddress();
        }
        return person.getOfficeAddress();

    }
}

