package de.adesso.fischereiregister.view.licenses_statistics.eventhandling;

import de.adesso.fischereiregister.core.events.LimitedLicenseCreatedEvent;
import de.adesso.fischereiregister.core.events.RegularLicenseCreatedEvent;
import de.adesso.fischereiregister.core.events.RegularLicenseDigitizedEvent;
import de.adesso.fischereiregister.core.events.VacationLicenseCreatedEvent;
import de.adesso.fischereiregister.core.model.type.SubmissionType;
import de.adesso.fischereiregister.registerservice.import_testdata.EventHandlerTimestampUtil;
import de.adesso.fischereiregister.view.licenses_statistics.services.LicensesStatisticsViewService;
import lombok.AllArgsConstructor;
import org.axonframework.eventhandling.EventHandler;
import org.axonframework.eventhandling.ResetHandler;
import org.axonframework.eventhandling.Timestamp;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.ZoneId;

@Component
@AllArgsConstructor
public class LicensesStatisticsViewEventHandler {

    private final LicensesStatisticsViewService licensesStatisticsViewService;
    private final EventHandlerTimestampUtil timestampUtil;

    @ResetHandler
    public void onReset() {
        licensesStatisticsViewService.deleteAll();
    }

    @EventHandler
    public void on(RegularLicenseDigitizedEvent event, @Timestamp Instant timestamp) {
        Instant effectiveTimestamp = timestampUtil.getEffectiveTimestamp(timestamp);
        licensesStatisticsViewService.updateOrCreateStatistic(
                event.fishingLicense().getType(),
                event.jurisdiction().getFederalState(),
                event.issuedByOffice(),
                SubmissionType.ANALOG,// Digitization of Fishing License occurs always in office
                getTimestampYear(effectiveTimestamp)
        );
    }

    @EventHandler
    public void on(RegularLicenseCreatedEvent event, @Timestamp Instant timestamp) {
        licensesStatisticsViewService.updateOrCreateStatistic(
                event.fishingLicense().getType(),
                event.jurisdiction().getFederalState(),
                event.issuedByOffice(),
                event.submissionType(),
                getTimestampYear(timestamp)
        );
    }

    @EventHandler
    public void on(LimitedLicenseCreatedEvent event, @Timestamp Instant timestamp) {
        // Extract the year from the timestamp
        final int year = timestamp.atZone(ZoneId.systemDefault()).getYear();

        licensesStatisticsViewService.updateOrCreateStatistic(
                event.fishingLicense().getType(),
                event.jurisdiction().getFederalState(),
                event.issuedByOffice(),
                event.submissionType(),
                year
        );
    }

    @EventHandler
    public void on(VacationLicenseCreatedEvent event, @Timestamp Instant timestamp) {
        licensesStatisticsViewService.updateOrCreateStatistic(
                event.fishingLicense().getType(),
                event.fishingLicense().getIssuingFederalState().toString(),
                event.issuedByOffice(),
                event.submissionType(),
                getTimestampYear(timestamp)
        );
    }

    private int getTimestampYear(Instant timestamp) {
        return timestamp.atZone(ZoneId.systemDefault()).getYear();
    }
}
