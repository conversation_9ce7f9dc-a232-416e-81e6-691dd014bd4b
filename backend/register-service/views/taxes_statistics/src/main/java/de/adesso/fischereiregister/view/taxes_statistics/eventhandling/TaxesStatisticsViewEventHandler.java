package de.adesso.fischereiregister.view.taxes_statistics.eventhandling;

import de.adesso.fischereiregister.core.events.FishingTaxPayedEvent;
import de.adesso.fischereiregister.core.events.JurisdictionMovedEvent;
import de.adesso.fischereiregister.core.events.LicenseExtendedEvent;
import de.adesso.fischereiregister.core.events.PersonCreatedEvent;
import de.adesso.fischereiregister.core.events.PersonalDataChangedEvent;
import de.adesso.fischereiregister.core.events.RegularLicenseCreatedEvent;
import de.adesso.fischereiregister.core.events.RegularLicenseDigitizedEvent;
import de.adesso.fischereiregister.core.events.ReplacementCardOrderedEvent;
import de.adesso.fischereiregister.core.events.VacationLicenseCreatedEvent;
import de.adesso.fischereiregister.core.model.Tax;
import de.adesso.fischereiregister.core.model.type.SubmissionType;
import de.adesso.fischereiregister.registerservice.import_testdata.MockedTimestampService;
import de.adesso.fischereiregister.view.taxes_statistics.services.TaxesStatisticsViewService;
import lombok.AllArgsConstructor;
import org.axonframework.eventhandling.EventHandler;
import org.axonframework.eventhandling.ResetHandler;
import org.axonframework.eventhandling.Timestamp;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.ZoneId;

@Component
@AllArgsConstructor
public class TaxesStatisticsViewEventHandler {

    private final TaxesStatisticsViewService taxesStatisticsViewService;
    private final MockedTimestampService mockedTimestampService;

    @ResetHandler
    public void onReset() {
        taxesStatisticsViewService.deleteAll(); // clear projection table before replay
    }

    @EventHandler
    public void on(FishingTaxPayedEvent event, @Timestamp Instant timestamp) {
        Instant effectiveTimestamp = mockedTimestampService.getEffectiveTimestamp(timestamp);
        event.taxes().stream()
                .filter(tax -> tax.getPaymentInfo().getAmount() > 0) // ignore previously paid taxes (or taxes without "payment"), because they re not relevant for hte statistics
                .forEach(tax -> taxesStatisticsViewService.updateOrCreateStatistic(
                        tax.getFederalState(),
                        event.issuedByOffice(),
                        event.submissionType(),
                        getTaxDuration(tax),
                        getTimestampYear(effectiveTimestamp),
                        tax.getPaymentInfo().getAmount()
                ));
    }

    @EventHandler
    public void on(RegularLicenseDigitizedEvent event, @Timestamp Instant timestamp) {
        Instant effectiveTimestamp = mockedTimestampService.getEffectiveTimestamp(timestamp);
        event.taxes().stream()
                .filter(tax -> tax.getPaymentInfo().getAmount() > 0) // ignore previously paid taxes (or taxes without "payment"), because they re not relevant for hte statistics
                .forEach(tax -> taxesStatisticsViewService.updateOrCreateStatistic(
                        tax.getFederalState(),
                        event.issuedByOffice(),
                        SubmissionType.ANALOG,
                        getTaxDuration(tax),
                        getTimestampYear(effectiveTimestamp),
                        tax.getPaymentInfo().getAmount()
                ));
    }


    @EventHandler
    public void on(RegularLicenseCreatedEvent event, @Timestamp Instant timestamp) {
        Instant effectiveTimestamp = mockedTimestampService.getEffectiveTimestamp(timestamp);
        event.taxes().stream()
                .filter(tax -> tax.getPaymentInfo().getAmount() > 0) // ignore previously paid taxes (or taxes without "payment"), because they re not relevant for hte statistics
                .forEach(tax -> taxesStatisticsViewService.updateOrCreateStatistic(
                        tax.getFederalState(),
                        event.issuedByOffice(),
                        event.submissionType(),
                        getTaxDuration(tax),
                        getTimestampYear(effectiveTimestamp),
                        tax.getPaymentInfo().getAmount()
                ));
    }


    @EventHandler
    public void on(VacationLicenseCreatedEvent event, @Timestamp Instant timestamp) {
        Instant effectiveTimestamp = mockedTimestampService.getEffectiveTimestamp(timestamp);
        event.taxes().stream()
                .filter(tax -> tax.getPaymentInfo().getAmount() > 0) // ignore previously paid taxes (or taxes without "payment"), because they re not relevant for hte statistics
                .forEach(tax -> taxesStatisticsViewService.updateOrCreateStatistic(
                        tax.getFederalState(),
                        event.issuedByOffice(),
                        event.submissionType(),
                        getTaxDuration(tax),
                        getTimestampYear(effectiveTimestamp),
                        tax.getPaymentInfo().getAmount()
                ));
    }


    @EventHandler
    public void on(LicenseExtendedEvent event, @Timestamp Instant timestamp) {
        Instant effectiveTimestamp = mockedTimestampService.getEffectiveTimestamp(timestamp);
        event.taxes().stream()
                .filter(tax -> tax.getPaymentInfo().getAmount() > 0) // ignore previously paid taxes (or taxes without "payment"), because they re not relevant for hte statistics
                .forEach(tax -> taxesStatisticsViewService.updateOrCreateStatistic(
                        tax.getFederalState(),
                        event.issuedByOffice(),
                        event.submissionType(),
                        getTaxDuration(tax),
                        getTimestampYear(effectiveTimestamp),
                        tax.getPaymentInfo().getAmount()
                ));
    }

    @EventHandler
    public void on(ReplacementCardOrderedEvent event, @Timestamp Instant timestamp) {
        Instant effectiveTimestamp = mockedTimestampService.getEffectiveTimestamp(timestamp);
        event.taxes().stream()
                .filter(tax -> tax.getPaymentInfo().getAmount() > 0) // ignore previously paid taxes (or taxes without "payment"), because they re not relevant for hte statistics
                .forEach(tax -> taxesStatisticsViewService.updateOrCreateStatistic(
                        tax.getFederalState(),
                        event.issuedByOffice(),
                        event.submissionType(),
                        getTaxDuration(tax),
                        getTimestampYear(effectiveTimestamp),
                        tax.getPaymentInfo().getAmount()
                ));
    }

    @EventHandler
    public void on(JurisdictionMovedEvent event, @Timestamp Instant timestamp) {
        Instant effectiveTimestamp = mockedTimestampService.getEffectiveTimestamp(timestamp);
        event.taxes().stream()
                .filter(tax -> tax.getPaymentInfo().getAmount() > 0) // ignore previously paid taxes (or taxes without "payment"), because they re not relevant for hte statistics
                .forEach(tax -> taxesStatisticsViewService.updateOrCreateStatistic(
                        tax.getFederalState(),
                        event.issuedByOffice(),
                        event.submissionType(),
                        getTaxDuration(tax),
                        getTimestampYear(effectiveTimestamp),
                        tax.getPaymentInfo().getAmount()
                ));
    }

    @EventHandler
    public void on(PersonCreatedEvent event, @Timestamp Instant timestamp) {
        Instant effectiveTimestamp = mockedTimestampService.getEffectiveTimestamp(timestamp);
        event.taxes().stream()
                .filter(tax -> tax.getPaymentInfo().getAmount() > 0) // ignore previously paid taxes (or taxes without "payment"), because they re not relevant for hte statistics
                .forEach(tax -> taxesStatisticsViewService.updateOrCreateStatistic(
                        tax.getFederalState(),
                        event.issuedByOffice(),
                        event.submissionType(),
                        getTaxDuration(tax),
                        getTimestampYear(effectiveTimestamp),
                        tax.getPaymentInfo().getAmount()
                ));
    }

    @EventHandler
    public void on(PersonalDataChangedEvent event, @Timestamp Instant timestamp) {
        Instant effectiveTimestamp = mockedTimestampService.getEffectiveTimestamp(timestamp);
        event.taxes().stream()
                .filter(tax -> tax.getPaymentInfo().getAmount() > 0) // ignore previously paid taxes (or taxes without "payment"), because they re not relevant for hte statistics
                .forEach(tax -> taxesStatisticsViewService.updateOrCreateStatistic(
                        tax.getFederalState(),
                        event.issuedByOffice(),
                        SubmissionType.ANALOG,
                        getTaxDuration(tax),
                        getTimestampYear(effectiveTimestamp),
                        tax.getPaymentInfo().getAmount()
                ));
    }

    private int getTaxDuration(Tax tax) {
        return tax.getValidTo().getYear() - tax.getValidFrom().getYear() + 1;
    }

    private int getTimestampYear(Instant timestamp) {
        return timestamp.atZone(ZoneId.systemDefault()).getYear();
    }
}
