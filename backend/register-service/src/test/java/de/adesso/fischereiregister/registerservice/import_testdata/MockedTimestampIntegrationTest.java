package de.adesso.fischereiregister.registerservice.import_testdata;

import de.adesso.fischereiregister.RegisterServiceApplication;
import de.adesso.fischereiregister.registerservice.common.security.mocking.WithMockSecurityContext;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.time.Instant;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Integration test to demonstrate how the MockedTimestampService works
 * with EventHandlers during test data import.
 */
@SpringBootTest(classes = RegisterServiceApplication.class)
@ActiveProfiles("test")
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@WithMockSecurityContext
class MockedTimestampIntegrationTest {

    @Autowired
    private MockedTimestampService mockedTimestampService;

    @Autowired
    private EventHandlerTimestampUtil timestampUtil;

    @BeforeEach
    void setUp() {
        mockedTimestampService.clearMockedTimestamps();
    }

    @Test
    void testMockedTimestampServiceIsAvailable() {
        // then
        assertNotNull(mockedTimestampService);
        assertNotNull(timestampUtil);
    }

    @Test
    void testEventHandlerTimestampUtilWithMockedTimestamp() {
        // given
        Instant realTimestamp = Instant.now();
        Instant mockedTimestamp = Instant.now().minusSeconds(3600);
        
        // when no mocked timestamp
        Instant result1 = timestampUtil.getEffectiveTimestamp(realTimestamp);
        
        // then
        assertEquals(realTimestamp, result1);
        
        // when mocked timestamp is available
        mockedTimestampService.pushMockedTimestamp(mockedTimestamp);
        Instant result2 = timestampUtil.getEffectiveTimestamp(realTimestamp);
        
        // then
        assertEquals(mockedTimestamp, result2);
    }

    @Test
    void testEventHandlerTimestampUtilWithMultipleMockedTimestamps() {
        // given
        Instant realTimestamp = Instant.now();
        Instant mockedTimestamp1 = Instant.now().minusSeconds(7200);
        Instant mockedTimestamp2 = Instant.now().minusSeconds(3600);
        
        // when
        mockedTimestampService.pushMockedTimestamp(mockedTimestamp1);
        mockedTimestampService.pushMockedTimestamp(mockedTimestamp2);
        
        // then - should use the most recent (top of stack)
        assertEquals(mockedTimestamp2, timestampUtil.getEffectiveTimestamp(realTimestamp));
        
        // when consuming
        Instant consumed = timestampUtil.getEffectiveTimestampAndConsume(realTimestamp);
        
        // then
        assertEquals(mockedTimestamp2, consumed);
        assertEquals(mockedTimestamp1, timestampUtil.getEffectiveTimestamp(realTimestamp)); // Next in stack
    }

    @Test
    void testMockedTimestampFromStringParsing() {
        // given
        String timestampString = "15.06.2023 14:30:00";
        Instant realTimestamp = Instant.now();
        
        // when
        mockedTimestampService.pushMockedTimestamp(timestampString);
        Instant result = timestampUtil.getEffectiveTimestamp(realTimestamp);
        
        // then
        assertNotNull(result);
        assertNotEquals(realTimestamp, result);
        assertTrue(mockedTimestampService.hasMockedTimestamp());
    }

    @Test
    void testThreadSafetyOfMockedTimestamps() {
        // given
        Instant timestamp1 = Instant.now().minusSeconds(1000);
        Instant timestamp2 = Instant.now().minusSeconds(2000);
        
        // when - simulate different threads (though this is a simple test)
        mockedTimestampService.pushMockedTimestamp(timestamp1);
        
        // then
        assertEquals(timestamp1, timestampUtil.getEffectiveTimestamp(Instant.now()));
        
        // when - clear and add different timestamp
        mockedTimestampService.clearMockedTimestamps();
        mockedTimestampService.pushMockedTimestamp(timestamp2);
        
        // then
        assertEquals(timestamp2, timestampUtil.getEffectiveTimestamp(Instant.now()));
    }
}
