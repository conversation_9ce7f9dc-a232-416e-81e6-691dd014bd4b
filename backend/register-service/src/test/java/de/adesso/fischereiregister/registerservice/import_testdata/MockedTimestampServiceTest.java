package de.adesso.fischereiregister.registerservice.import_testdata;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;

import static org.junit.jupiter.api.Assertions.*;

class MockedTimestampServiceTest {

    private MockedTimestampService mockedTimestampService;

    @BeforeEach
    void setUp() {
        mockedTimestampService = new MockedTimestampService();
    }

    @Test
    void testPushAndPeekMockedTimestamp() {
        // given
        Instant testTimestamp = Instant.now();
        
        // when
        mockedTimestampService.pushMockedTimestamp(testTimestamp);
        
        // then
        assertTrue(mockedTimestampService.hasMockedTimestamp());
        assertEquals(testTimestamp, mockedTimestampService.peekMockedTimestamp());
    }

    @Test
    void testPushMockedTimestampFromString() {
        // given
        String timestampString = "01.01.2023 12:00:00";
        
        // when
        mockedTimestampService.pushMockedTimestamp(timestampString);
        
        // then
        assertTrue(mockedTimestampService.hasMockedTimestamp());
        Instant result = mockedTimestampService.peekMockedTimestamp();
        assertNotNull(result);
        
        LocalDateTime expectedDateTime = LocalDateTime.of(2023, 1, 1, 12, 0, 0);
        Instant expectedInstant = expectedDateTime.atZone(ZoneId.systemDefault()).toInstant();
        assertEquals(expectedInstant, result);
    }

    @Test
    void testPushMockedTimestampFromDateOnlyString() {
        // given
        String timestampString = "01.01.2023";
        
        // when
        mockedTimestampService.pushMockedTimestamp(timestampString);
        
        // then
        assertTrue(mockedTimestampService.hasMockedTimestamp());
        Instant result = mockedTimestampService.peekMockedTimestamp();
        assertNotNull(result);
        
        // Should default to 12:00:00
        LocalDateTime expectedDateTime = LocalDateTime.of(2023, 1, 1, 12, 0, 0);
        Instant expectedInstant = expectedDateTime.atZone(ZoneId.systemDefault()).toInstant();
        assertEquals(expectedInstant, result);
    }

    @Test
    void testGetEffectiveTimestamp() {
        // given
        Instant realTimestamp = Instant.now();
        Instant mockedTimestamp = Instant.now().minusSeconds(3600);
        
        // when no mocked timestamp
        Instant result1 = mockedTimestampService.getEffectiveTimestamp(realTimestamp);
        
        // then
        assertEquals(realTimestamp, result1);
        
        // when mocked timestamp is available
        mockedTimestampService.pushMockedTimestamp(mockedTimestamp);
        Instant result2 = mockedTimestampService.getEffectiveTimestamp(realTimestamp);
        
        // then
        assertEquals(mockedTimestamp, result2);
    }

    @Test
    void testGetEffectiveTimestampAndConsume() {
        // given
        Instant realTimestamp = Instant.now();
        Instant mockedTimestamp = Instant.now().minusSeconds(3600);
        
        // when no mocked timestamp
        Instant result1 = mockedTimestampService.getEffectiveTimestampAndConsume(realTimestamp);
        
        // then
        assertEquals(realTimestamp, result1);
        assertFalse(mockedTimestampService.hasMockedTimestamp());
        
        // when mocked timestamp is available
        mockedTimestampService.pushMockedTimestamp(mockedTimestamp);
        assertTrue(mockedTimestampService.hasMockedTimestamp());
        
        Instant result2 = mockedTimestampService.getEffectiveTimestampAndConsume(realTimestamp);
        
        // then
        assertEquals(mockedTimestamp, result2);
        assertFalse(mockedTimestampService.hasMockedTimestamp()); // Should be consumed
    }

    @Test
    void testStackBehavior() {
        // given
        Instant timestamp1 = Instant.now().minusSeconds(3600);
        Instant timestamp2 = Instant.now().minusSeconds(1800);
        
        // when
        mockedTimestampService.pushMockedTimestamp(timestamp1);
        mockedTimestampService.pushMockedTimestamp(timestamp2);
        
        // then
        assertEquals(2, mockedTimestampService.getStackSize());
        assertEquals(timestamp2, mockedTimestampService.popMockedTimestamp()); // LIFO
        assertEquals(timestamp1, mockedTimestampService.popMockedTimestamp());
        assertEquals(0, mockedTimestampService.getStackSize());
    }

    @Test
    void testClearMockedTimestamps() {
        // given
        mockedTimestampService.pushMockedTimestamp(Instant.now());
        mockedTimestampService.pushMockedTimestamp(Instant.now().minusSeconds(100));
        
        // when
        mockedTimestampService.clearMockedTimestamps();
        
        // then
        assertFalse(mockedTimestampService.hasMockedTimestamp());
        assertEquals(0, mockedTimestampService.getStackSize());
    }

    @Test
    void testInvalidTimestampString() {
        // given
        String invalidTimestamp = "invalid-date";
        
        // when
        mockedTimestampService.pushMockedTimestamp(invalidTimestamp);
        
        // then
        assertFalse(mockedTimestampService.hasMockedTimestamp());
    }

    @Test
    void testNullAndEmptyTimestampStrings() {
        // when
        mockedTimestampService.pushMockedTimestamp((String) null);
        mockedTimestampService.pushMockedTimestamp("");
        mockedTimestampService.pushMockedTimestamp("   ");
        
        // then
        assertFalse(mockedTimestampService.hasMockedTimestamp());
    }
}
