package de.adesso.fischereiregister.registerservice.import_testdata;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.Instant;

/**
 * Utility class for EventHandlers to get the appropriate timestamp.
 * This class provides a centralized way for all EventHandlers to determine
 * whether to use a mocked timestamp (during test data import) or the real timestamp.
 */
@Component
@Slf4j
public class EventHandlerTimestampUtil {

    private final MockedTimestampService mockedTimestampService;

    @Autowired
    public EventHandlerTimestampUtil(MockedTimestampService mockedTimestampService) {
        this.mockedTimestampService = mockedTimestampService;
    }

    /**
     * Gets the appropriate timestamp for use in EventHandlers.
     * Returns the mocked timestamp if available, otherwise returns the real timestamp.
     * 
     * @param realTimestamp the real timestamp from @Timestamp annotation
     * @return the mocked timestamp if available, otherwise the real timestamp
     */
    public Instant getEffectiveTimestamp(Instant realTimestamp) {
        return mockedTimestampService.getEffectiveTimestamp(realTimestamp);
    }

    /**
     * Gets the appropriate timestamp for use in EventHandlers and consumes the mocked timestamp.
     * Returns the mocked timestamp if available (and removes it from stack), otherwise returns the real timestamp.
     * 
     * @param realTimestamp the real timestamp from @Timestamp annotation
     * @return the mocked timestamp if available (consumed), otherwise the real timestamp
     */
    public Instant getEffectiveTimestampAndConsume(Instant realTimestamp) {
        return mockedTimestampService.getEffectiveTimestampAndConsume(realTimestamp);
    }

    /**
     * Checks if there are any mocked timestamps available.
     * 
     * @return true if there are mocked timestamps available
     */
    public boolean hasMockedTimestamp() {
        return mockedTimestampService.hasMockedTimestamp();
    }
}
