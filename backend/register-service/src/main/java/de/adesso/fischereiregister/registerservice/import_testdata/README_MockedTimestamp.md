# MockedTimestamp Service

This document describes the MockedTimestamp service implementation that allows EventHandlers to use mocked timestamps during test data import instead of real timestamps.

## Overview

The MockedTimestamp service provides a way to control timestamps in EventHandlers during test data import. This is useful for:
- Creating consistent test data with specific timestamps
- Testing time-dependent functionality
- Ensuring reproducible test results

## Components

### 1. MockedTimestampService
- **Location**: `backend/register-service/src/main/java/de/adesso/fischereiregister/registerservice/import_testdata/MockedTimestampService.java`
- **Purpose**: Manages a thread-local stack of mocked timestamps
- **Key Methods**:
  - `pushMockedTimestamp(Instant timestamp)` - Adds a timestamp to the stack
  - `pushMockedTimestamp(String timestampString)` - Parses and adds a timestamp from string
  - `getEffectiveTimestamp(Instant realTimestamp)` - Returns mocked timestamp if available, otherwise real timestamp
  - `getEffectiveTimestampAndConsume(Instant realTimestamp)` - Same as above but consumes the mocked timestamp
  - `clearMockedTimestamps()` - Clears all mocked timestamps

### 2. Direct Integration
- **Approach**: MockedTimestampService is injected directly into EventHandler classes
- **Usage**: Call `mockedTimestampService.getEffectiveTimestamp(timestamp)` in EventHandler methods

### 3. CSV Integration
- **New Column**: `mocked_timestamp` added to CSV headers
- **Format**: Supports "dd.MM.yyyy HH:mm:ss" and "dd.MM.yyyy" (defaults to 12:00:00)
- **Integration**: ImportTestDataServiceImpl automatically pushes mocked timestamps from CSV before sending commands

## Usage

### For EventHandlers

1. **Inject MockedTimestampService**:
```java
@Component
public class MyEventHandler {
    private final MyService myService;
    private final MockedTimestampService mockedTimestampService;

    public MyEventHandler(MyService myService, MockedTimestampService mockedTimestampService) {
        this.myService = myService;
        this.mockedTimestampService = mockedTimestampService;
    }
}
```

2. **Use in EventHandler methods**:
```java
@EventHandler
public void on(SomeEvent event, @Timestamp Instant timestamp) {
    Instant effectiveTimestamp = mockedTimestampService.getEffectiveTimestamp(timestamp);
    // Use effectiveTimestamp instead of timestamp
    myService.processEvent(event, effectiveTimestamp);
}
```

### For CSV Test Data

Add a `mocked_timestamp` column to your CSV file:

```csv
register_id,first_name,last_name,mocked_timestamp,...
12345678-1234-1234-1234-123456789012,John,Doe,01.01.2023 10:00:00,...
12345678-1234-1234-1234-123456789013,Jane,Smith,02.01.2023,...
```

### For Programmatic Use

```java
@Autowired
private MockedTimestampService mockedTimestampService;

// Push a mocked timestamp
mockedTimestampService.pushMockedTimestamp("01.01.2023 12:00:00");

// Or push an Instant
mockedTimestampService.pushMockedTimestamp(Instant.now().minusSeconds(3600));

// Clear all mocked timestamps
mockedTimestampService.clearMockedTimestamps();
```

## Implementation Details

### Thread Safety
- Uses `ThreadLocal<Deque<Instant>>` to ensure thread safety
- Each thread has its own stack of mocked timestamps

### Stack Behavior
- Mocked timestamps are stored in a LIFO (Last In, First Out) stack
- `getEffectiveTimestamp()` peeks at the top without removing it
- `getEffectiveTimestampAndConsume()` pops the top timestamp

### Date Parsing
- Supports German date format: "dd.MM.yyyy HH:mm:ss"
- Falls back to date-only format: "dd.MM.yyyy" (defaults to 12:00:00)
- Invalid dates are logged as warnings and ignored

### Integration with Test Data Import
- `ImportTestDataServiceImpl` automatically clears timestamps at the start of import
- For each CSV record, if `mocked_timestamp` column has a value, it's pushed to the stack
- Commands are then sent, and EventHandlers can use the mocked timestamp

## Example EventHandler Updates

### Before (using real timestamp):
```java
@EventHandler
public void on(PersonCreatedEvent event, @Timestamp Instant timestamp) {
    ProcessHeaderData processHeaderData = ProcessHeaderData.builder()
            .registerEntryId(event.registerId())
            .timestamp(timestamp)  // Real timestamp
            .build();
    // ...
}
```

### After (using effective timestamp):
```java
@EventHandler
public void on(PersonCreatedEvent event, @Timestamp Instant timestamp) {
    Instant effectiveTimestamp = mockedTimestampService.getEffectiveTimestamp(timestamp);
    ProcessHeaderData processHeaderData = ProcessHeaderData.builder()
            .registerEntryId(event.registerId())
            .timestamp(effectiveTimestamp)  // Mocked timestamp if available
            .build();
    // ...
}
```

## Testing

- Unit tests: `MockedTimestampServiceTest.java`
- Integration tests: `MockedTimestampIntegrationTest.java`

## Migration Guide

To update existing EventHandlers:

1. Add `MockedTimestampService` to constructor dependencies
2. Replace direct `timestamp` usage with `mockedTimestampService.getEffectiveTimestamp(timestamp)`
3. Update any timestamp-related logic to use the effective timestamp

## Benefits

- **Consistent Test Data**: Create test data with specific, controlled timestamps
- **Reproducible Tests**: Same CSV data always produces same timestamps
- **Time-based Testing**: Test functionality that depends on specific dates/times
- **Backward Compatibility**: When no mocked timestamps are provided, real timestamps are used
- **Thread Safe**: Each thread maintains its own timestamp stack
