package de.adesso.fischereiregister.registerservice.import_testdata;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayDeque;
import java.util.Deque;

/**
 * Service for managing mocked timestamps during test data import.
 * This service maintains a stack of mocked timestamps that can be used
 * instead of real timestamps during event handling when importing test data.
 */
@Service
@Slf4j
public class MockedTimestampService {

    private static final DateTimeFormatter GERMAN_DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("dd.MM.yyyy HH:mm:ss");
    private static final DateTimeFormatter GERMAN_DATE_FORMATTER = DateTimeFormatter.ofPattern("dd.MM.yyyy");
    
    private final ThreadLocal<Deque<Instant>> mockedTimestampStack = ThreadLocal.withInitial(ArrayDeque::new);

    /**
     * Pushes a mocked timestamp onto the stack.
     * 
     * @param timestamp the timestamp to push
     */
    public void pushMockedTimestamp(Instant timestamp) {
        if (timestamp != null) {
            mockedTimestampStack.get().push(timestamp);
            log.debug("Pushed mocked timestamp: {}", timestamp);
        }
    }

    /**
     * Pushes a mocked timestamp parsed from a string onto the stack.
     * Supports formats: "dd.MM.yyyy HH:mm:ss" and "dd.MM.yyyy" (defaults to 12:00:00)
     * 
     * @param timestampString the timestamp string to parse and push
     */
    public void pushMockedTimestamp(String timestampString) {
        if (timestampString != null && !timestampString.trim().isEmpty()) {
            try {
                Instant timestamp = parseTimestamp(timestampString.trim());
                pushMockedTimestamp(timestamp);
            } catch (DateTimeParseException e) {
                log.warn("Failed to parse mocked timestamp '{}': {}", timestampString, e.getMessage());
            }
        }
    }

    /**
     * Pops the most recent mocked timestamp from the stack.
     * 
     * @return the popped timestamp, or null if stack is empty
     */
    public Instant popMockedTimestamp() {
        Deque<Instant> stack = mockedTimestampStack.get();
        if (!stack.isEmpty()) {
            Instant timestamp = stack.pop();
            log.debug("Popped mocked timestamp: {}", timestamp);
            return timestamp;
        }
        return null;
    }

    /**
     * Peeks at the most recent mocked timestamp without removing it from the stack.
     * 
     * @return the most recent timestamp, or null if stack is empty
     */
    public Instant peekMockedTimestamp() {
        Deque<Instant> stack = mockedTimestampStack.get();
        return stack.isEmpty() ? null : stack.peek();
    }

    /**
     * Checks if there are any mocked timestamps in the stack.
     * 
     * @return true if there are mocked timestamps available
     */
    public boolean hasMockedTimestamp() {
        return !mockedTimestampStack.get().isEmpty();
    }

    /**
     * Clears all mocked timestamps from the stack.
     */
    public void clearMockedTimestamps() {
        Deque<Instant> stack = mockedTimestampStack.get();
        int size = stack.size();
        stack.clear();
        if (size > 0) {
            log.debug("Cleared {} mocked timestamps from stack", size);
        }
    }

    /**
     * Gets the appropriate timestamp to use in event handlers.
     * Returns the mocked timestamp if available, otherwise returns the provided real timestamp.
     * 
     * @param realTimestamp the real timestamp from @Timestamp annotation
     * @return the mocked timestamp if available, otherwise the real timestamp
     */
    public Instant getEffectiveTimestamp(Instant realTimestamp) {
        Instant mockedTimestamp = peekMockedTimestamp();
        if (mockedTimestamp != null) {
            log.debug("Using mocked timestamp: {} instead of real timestamp: {}", mockedTimestamp, realTimestamp);
            return mockedTimestamp;
        }
        return realTimestamp;
    }

    /**
     * Gets the appropriate timestamp to use in event handlers and consumes the mocked timestamp.
     * Returns the mocked timestamp if available (and removes it from stack), otherwise returns the provided real timestamp.
     * 
     * @param realTimestamp the real timestamp from @Timestamp annotation
     * @return the mocked timestamp if available (consumed), otherwise the real timestamp
     */
    public Instant getEffectiveTimestampAndConsume(Instant realTimestamp) {
        Instant mockedTimestamp = popMockedTimestamp();
        if (mockedTimestamp != null) {
            log.debug("Using and consuming mocked timestamp: {} instead of real timestamp: {}", mockedTimestamp, realTimestamp);
            return mockedTimestamp;
        }
        return realTimestamp;
    }

    /**
     * Parses a timestamp string in German format.
     * 
     * @param timestampString the timestamp string to parse
     * @return the parsed Instant
     * @throws DateTimeParseException if the string cannot be parsed
     */
    private Instant parseTimestamp(String timestampString) throws DateTimeParseException {
        try {
            // Try parsing with full date-time format first
            LocalDateTime dateTime = LocalDateTime.parse(timestampString, GERMAN_DATE_TIME_FORMATTER);
            return dateTime.atZone(ZoneId.systemDefault()).toInstant();
        } catch (DateTimeParseException e) {
            // Fall back to date-only format, default to 12:00:00
            try {
                LocalDateTime dateTime = LocalDateTime.parse(timestampString + " 12:00:00", GERMAN_DATE_TIME_FORMATTER);
                return dateTime.atZone(ZoneId.systemDefault()).toInstant();
            } catch (DateTimeParseException e2) {
                throw new DateTimeParseException("Unable to parse timestamp: " + timestampString, timestampString, 0);
            }
        }
    }

    /**
     * Gets the current size of the mocked timestamp stack.
     * 
     * @return the number of mocked timestamps in the stack
     */
    public int getStackSize() {
        return mockedTimestampStack.get().size();
    }
}
